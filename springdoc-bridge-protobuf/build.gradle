description = "SpringDoc Bridge for Protocol Buffers"

dependencies {
    api(project(":jackson-module-protobuf"))

    compileOnly("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
    compileOnly("tools.jackson.core:jackson-databind:${jackson3Version}")
    compileOnly("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    compileOnly("org.springdoc:springdoc-openapi-starter-common:${springDocsVersion}")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
    testImplementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    testImplementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocsVersion}")
    testImplementation("tools.jackson.core:jackson-databind:${jackson3Version}")
}

apply from: "${rootDir}/gradle/protobuf.gradle"
apply from: "${rootDir}/gradle/deploy.gradle"
