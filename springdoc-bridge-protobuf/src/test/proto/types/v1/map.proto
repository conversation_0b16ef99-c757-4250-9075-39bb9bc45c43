syntax = "proto3";

package types.v1;

option java_multiple_files = true;

message MapTestMessage {
  map<string, string> metadata = 1;
  map<string, Status> status_map = 2;
  map<string, Address> address_map = 3;
  map<string, int32> score_map = 4;
  map<string, bool> feature_flags = 5;
  map<string, string> deprecated_map = 6 [deprecated = true];

  enum Status {
    STATUS_UNSPECIFIED = 0;
    ACTIVE = 1;
    INACTIVE = 2;
    PENDING = 3;
  }

  message Address {
    string street = 1;
    string city = 2;
    string country = 3;
  }
}
