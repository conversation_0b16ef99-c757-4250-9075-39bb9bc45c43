package jacksonmodule.protobuf.nativejackson;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.protobuf.ProtocolMessageEnum;
import java.io.IOException;

/**
 * Native Jackson serializer for protobuf enums that doesn't depend on JsonFormat.
 */
@SuppressWarnings("rawtypes")
final class NativeProtobufEnumSerializer extends JsonSerializer<Object> {

    private final NativeJacksonProtobufModule.Options options;

    public NativeProtobufEnumSerializer(NativeJacksonProtobufModule.Options options) {
        this.options = options;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        ProtocolMessageEnum enumValue = (ProtocolMessageEnum) value;
        if (options.serializeEnumAsInt()) {
            gen.writeNumber(enumValue.getNumber());
        } else {
            gen.writeString(((Enum<?>) enumValue).name());
        }
    }
}
