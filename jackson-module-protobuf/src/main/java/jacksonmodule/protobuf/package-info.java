/**
 * <PERSON> Module for Protocol Buffers support.
 *
 * <p>This package provides comprehensive Jackson serialization and deserialization support for
 * Protocol Buffers messages and enums, following the official Protobuf JSON Mapping specification.
 *
 * <p>The module supports both Jackson 2.x and Jackson 3.x:
 * <ul>
 *   <li>For Jackson 2.x: Use {@link jacksonmodule.protobuf.ProtobufModule}</li>
 *   <li>For Jackson 3.x: Use {@link jacksonmodule.protobuf.v3.ProtobufModule}</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 0.1.0
 */
package jacksonmodule.protobuf;
