syntax = "proto3";

package types.v1;

import "google/protobuf/any.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";

option java_multiple_files = true;
option java_package = "types.v1";

message TypesTest {
  google.protobuf.Timestamp timestamp_field = 1;
  google.protobuf.Duration duration_field = 2;
  google.protobuf.BoolValue bool_wrapper = 3;
  google.protobuf.Int32Value int32_wrapper = 4;
  google.protobuf.Int64Value int64_wrapper = 5;
  google.protobuf.UInt32Value uint32_wrapper = 6;
  google.protobuf.UInt64Value uint64_wrapper = 7;
  google.protobuf.FloatValue float_wrapper = 8;
  google.protobuf.DoubleValue double_wrapper = 9;
  google.protobuf.StringValue string_wrapper = 10;
  google.protobuf.BytesValue bytes_wrapper = 11;
  google.protobuf.Any any_field = 12;
  google.protobuf.Struct struct_field = 13;
  google.protobuf.Value value_field = 14;
  google.protobuf.ListValue list_value_field = 15;
  google.protobuf.NullValue null_value_field = 16;
  google.protobuf.FieldMask field_mask = 17;
  google.protobuf.Empty empty_field = 18;
}
